import React, { useState, useCallback } from 'react'
import { useAuthStore } from '../store/authStore'

interface UploadFile {
  id: string
  file: File
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string
  response?: any
}

interface ImageUploadProps {
  onUploadComplete?: () => void
}

const ImageUpload: React.FC<ImageUploadProps> = ({ onUploadComplete }) => {
  const [files, setFiles] = useState<UploadFile[]>([])
  const [isDragActive, setIsDragActive] = useState(false)
  const { token } = useAuthStore()

  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api/v1'

  // File validation
  const validateFile = (file: File): string | null => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (!allowedTypes.includes(file.type)) {
      return 'Invalid file type. Only JPEG and PNG files are allowed.'
    }

    if (file.size > maxSize) {
      return 'File size exceeds 10MB limit.'
    }

    return null
  }

  // Handle file selection
  const handleFiles = useCallback((selectedFiles: FileList | File[]) => {
    const fileArray = Array.from(selectedFiles)
    const newFiles: UploadFile[] = []

    fileArray.forEach(file => {
      const error = validateFile(file)
      const uploadFile: UploadFile = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        file,
        status: error ? 'error' : 'pending',
        progress: 0,
        error,
      }
      newFiles.push(uploadFile)
    })

    setFiles(prev => [...prev, ...newFiles])

    // Auto-upload valid files
    newFiles.forEach(uploadFile => {
      if (uploadFile.status === 'pending') {
        uploadFile.status = 'uploading'
        uploadToServer(uploadFile)
      }
    })
  }, [])

  // Upload file to server
  const uploadToServer = async (uploadFile: UploadFile) => {
    try {
      const formData = new FormData()
      formData.append('file', uploadFile.file)

      const xhr = new XMLHttpRequest()

      // Track upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100)
          setFiles(prev => prev.map(f => 
            f.id === uploadFile.id ? { ...f, progress } : f
          ))
        }
      })

      // Handle completion
      xhr.addEventListener('load', () => {
        if (xhr.status === 201) {
          const response = JSON.parse(xhr.responseText)
          setFiles(prev => prev.map(f =>
            f.id === uploadFile.id
              ? { ...f, status: 'success', progress: 100, response: response.data }
              : f
          ))
          // Call the callback if provided
          if (onUploadComplete) {
            onUploadComplete()
          }
        } else {
          const errorResponse = JSON.parse(xhr.responseText)
          setFiles(prev => prev.map(f =>
            f.id === uploadFile.id
              ? { ...f, status: 'error', error: errorResponse.error?.message || 'Upload failed' }
              : f
          ))
        }
      })

      // Handle errors
      xhr.addEventListener('error', () => {
        setFiles(prev => prev.map(f => 
          f.id === uploadFile.id 
            ? { ...f, status: 'error', error: 'Network error occurred' }
            : f
        ))
      })

      xhr.open('POST', `${API_BASE_URL}/images/upload`)

      // Add authentication header
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`)
      }

      xhr.send(formData)

    } catch (error) {
      setFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { ...f, status: 'error', error: 'Upload failed' }
          : f
      ))
    }
  }

  // Drag and drop handlers
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(false)
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(false)

    const droppedFiles = e.dataTransfer.files
    if (droppedFiles.length > 0) {
      handleFiles(droppedFiles)
    }
  }, [handleFiles])

  // File input handler
  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files
    if (selectedFiles && selectedFiles.length > 0) {
      handleFiles(selectedFiles)
    }
    // Reset input value to allow selecting the same file again
    e.target.value = ''
  }, [handleFiles])

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div>
      {/* Upload Area */}
      <div
        className={`upload-area ${isDragActive ? 'drag-active' : ''}`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => document.getElementById('file-input')?.click()}
      >
        <div className="upload-icon">
          📁
        </div>
        <div className="upload-text">
          {isDragActive ? 'Drop files here' : 'Drag and drop images here'}
        </div>
        <div className="upload-subtext">
          or click to browse • PNG, JPG up to 10MB
        </div>
        <input
          id="file-input"
          type="file"
          multiple
          accept="image/png,image/jpeg,image/jpg"
          onChange={handleFileInput}
          style={{ display: 'none' }}
        />
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="file-list">
          <h3 style={{ marginBottom: '1rem', fontSize: '1.125rem', fontWeight: '600' }}>
            Uploaded Files ({files.length})
          </h3>
          {files.map(uploadFile => (
            <div key={uploadFile.id} className="file-item">
              <div className="file-info">
                <div>
                  <div className="file-name">{uploadFile.file.name}</div>
                  <div className="file-size">{formatFileSize(uploadFile.file.size)}</div>
                  {uploadFile.status === 'uploading' && (
                    <div className="progress-bar">
                      <div 
                        className="progress-fill" 
                        style={{ width: `${uploadFile.progress}%` }}
                      />
                    </div>
                  )}
                  {uploadFile.error && (
                    <div className="error-message">{uploadFile.error}</div>
                  )}
                  {uploadFile.response && (
                    <div style={{ fontSize: '0.75rem', color: '#64748b', marginTop: '0.25rem' }}>
                      Mode: {uploadFile.response.mode} • Tokens: {uploadFile.response.estimatedTokens}
                    </div>
                  )}
                </div>
              </div>
              <div className={`file-status status-${uploadFile.status}`}>
                {uploadFile.status === 'uploading' && `${uploadFile.progress}%`}
                {uploadFile.status === 'success' && '✓ Uploaded'}
                {uploadFile.status === 'error' && '✗ Failed'}
                {uploadFile.status === 'pending' && '⏳ Pending'}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default ImageUpload
