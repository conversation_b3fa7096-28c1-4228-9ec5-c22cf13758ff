Add-Type -AssemblyName System.Drawing

$bitmap = New-Object System.Drawing.Bitmap(400, 300)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# Fill background
$graphics.Clear([System.Drawing.Color]::LightGreen)

# Draw a purple rectangle
$brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Purple)
$graphics.FillRectangle($brush, 50, 50, 300, 60)

# Draw some text
$font = New-Object System.Drawing.Font("Arial", 16)
$textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$graphics.DrawString("Test Image 5", $font, $textBrush, 120, 70)

# Save the image
$bitmap.Save("test-ui-screenshot-5.png", [System.Drawing.Imaging.ImageFormat]::Png)

# Clean up
$graphics.Dispose()
$bitmap.Dispose()
$brush.Dispose()
$font.Dispose()
$textBrush.Dispose()

Write-Host "Test image created: test-ui-screenshot-5.png"
