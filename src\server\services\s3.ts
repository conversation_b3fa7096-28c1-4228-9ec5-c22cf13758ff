import AWS from 'aws-sdk'
import { v4 as uuidv4 } from 'uuid'
import sharp from 'sharp'

interface UploadResult {
  key: string
  bucket: string
  url: string
  thumbnailUrl?: string
}

interface ImageMetadata {
  width: number
  height: number
  format: string
  size: number
  hasAlpha: boolean
}

class S3ServiceClass {
  private s3: AWS.S3 | null = null
  private bucket: string = ''
  private cdnEndpoint: string = ''

  async initialize(): Promise<void> {
    try {
      // Check if we should run in mock mode
      const isMockMode = process.env.DO_SPACES_KEY === 'test' ||
                        !process.env.DO_SPACES_KEY ||
                        !process.env.DO_SPACES_SECRET

      if (isMockMode) {
        console.log('✓ DO Spaces running in mock mode (no real credentials)')
        return
      }

      // Configure for DigitalOcean Spaces
      const config: AWS.S3.ClientConfiguration = {
        accessKeyId: process.env.DO_SPACES_KEY,
        secretAccessKey: process.env.DO_SPACES_SECRET,
        endpoint: process.env.DO_SPACES_ENDPOINT, // e.g., 'nyc3.digitaloceanspaces.com'
        region: process.env.DO_SPACES_REGION || 'nyc3',
        s3ForcePathStyle: false, // DO Spaces uses virtual-hosted-style
        signatureVersion: 'v4',
      }

      this.s3 = new AWS.S3(config)
      this.bucket = process.env.DO_SPACES_BUCKET || 'i2d-uploads'
      this.cdnEndpoint = process.env.DO_SPACES_CDN_ENDPOINT || ''

      console.log('DigitalOcean Spaces service initialized successfully')
    } catch (error) {
      console.error('Failed to initialize DO Spaces service:', error)
      // Don't throw error in development
      if (process.env.NODE_ENV === 'production') {
        throw error
      }
    }
  }

  // Remove ensureBucketExists - DO Spaces buckets must be created manually

  async uploadImage(
    buffer: Buffer,
    originalFilename: string,
    mimeType: string
  ): Promise<UploadResult> {
    // Mock mode for development
    if (!this.s3) {
      const mockKey = `images/${uuidv4()}.jpg`
      return {
        key: mockKey,
        bucket: 'mock-bucket',
        url: `http://localhost:3001/mock/${mockKey}`,
        thumbnailUrl: `http://localhost:3001/mock/thumbnails/${uuidv4()}.jpg`,
      }
    }

    try {
      // Generate unique key
      const fileExtension = originalFilename.split('.').pop() || 'jpg'
      const key = `images/${uuidv4()}.${fileExtension}`

      // Process image with Sharp
      const processedImage = await this.processImage(buffer)

      // Upload original image
      const uploadParams: AWS.S3.PutObjectRequest = {
        Bucket: this.bucket,
        Key: key,
        Body: processedImage.buffer,
        ContentType: mimeType,
        ACL: 'public-read', // Make publicly accessible
        Metadata: {
          originalFilename,
          width: processedImage.metadata.width.toString(),
          height: processedImage.metadata.height.toString(),
          format: processedImage.metadata.format,
        },
      }

      await this.s3.upload(uploadParams).promise()

      // Generate thumbnail
      const thumbnailKey = `thumbnails/${uuidv4()}.jpg`
      const thumbnail = await this.generateThumbnail(buffer)
      
      const thumbnailParams: AWS.S3.PutObjectRequest = {
        Bucket: this.bucket,
        Key: thumbnailKey,
        Body: thumbnail,
        ContentType: 'image/jpeg',
        ACL: 'public-read',
      }

      await this.s3.upload(thumbnailParams).promise()

      // Generate URLs
      const url = this.getObjectUrl(key)
      const thumbnailUrl = this.getObjectUrl(thumbnailKey)

      return {
        key,
        bucket: this.bucket,
        url,
        thumbnailUrl,
      }
    } catch (error) {
      console.error('Failed to upload image to DO Spaces:', error)
      throw error
    }
  }

  private async processImage(buffer: Buffer): Promise<{
    buffer: Buffer
    metadata: ImageMetadata
  }> {
    try {
      const image = sharp(buffer)
      const metadata = await image.metadata()

      // Optimize image (reduce quality if too large)
      let processedBuffer = buffer
      if (buffer.length > 5 * 1024 * 1024) { // 5MB
        processedBuffer = await image
          .jpeg({ quality: 85 })
          .toBuffer()
      }

      return {
        buffer: processedBuffer,
        metadata: {
          width: metadata.width || 0,
          height: metadata.height || 0,
          format: metadata.format || 'unknown',
          size: processedBuffer.length,
          hasAlpha: metadata.hasAlpha || false,
        },
      }
    } catch (error) {
      console.error('Failed to process image:', error)
      throw error
    }
  }

  private async generateThumbnail(buffer: Buffer): Promise<Buffer> {
    try {
      return await sharp(buffer)
        .resize(300, 300, {
          fit: 'inside',
          withoutEnlargement: true,
        })
        .jpeg({ quality: 80 })
        .toBuffer()
    } catch (error) {
      console.error('Failed to generate thumbnail:', error)
      throw error
    }
  }

  private getObjectUrl(key: string): string {
    if (this.cdnEndpoint) {
      // Use CDN endpoint if available
      return `${this.cdnEndpoint}/${key}`
    } else {
      // Use direct Spaces endpoint
      const endpoint = process.env.DO_SPACES_ENDPOINT || 'nyc3.digitaloceanspaces.com'
      return `https://${this.bucket}.${endpoint}/${key}`
    }
  }

  async getObject(key: string, bucket?: string): Promise<Buffer> {
    // Mock mode for development - return a simple test buffer
    if (!this.s3) {
      console.log(`Mock mode: returning test buffer for key ${key}`)
      // Create a simple test image buffer using Sharp
      return await sharp({
        create: {
          width: 400,
          height: 300,
          channels: 3,
          background: { r: 200, g: 200, b: 200 }
        }
      })
      .png()
      .toBuffer()
    }

    try {
      const params = {
        Bucket: bucket || this.bucket,
        Key: key,
      }

      const result = await this.s3.getObject(params).promise()
      return result.Body as Buffer
    } catch (error) {
      console.error(`Failed to get object ${key}:`, error)
      throw error
    }
  }

  async deleteObject(key: string): Promise<void> {
    if (!this.s3) throw new Error('DO Spaces not initialized')

    await this.s3.deleteObject({
      Bucket: this.bucket,
      Key: key,
    }).promise()
  }

  async healthCheck(): Promise<boolean> {
    try {
      if (!this.s3) return true // Mock mode is always healthy
      await this.s3.headBucket({ Bucket: this.bucket }).promise()
      return true
    } catch (error) {
      console.error('DO Spaces health check failed:', error)
      return false
    }
  }
}

export const S3Service = new S3ServiceClass()
