<!DOCTYPE html>
<html>
<head>
    <title>Create Test Image</title>
</head>
<body>
    <canvas id="canvas" width="400" height="300"></canvas>
    <br>
    <button onclick="downloadImage()">Download Test Image</button>
    
    <script>
        // Create a simple test image
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Fill background
        ctx.fillStyle = '#f0f0f0';
        ctx.fillRect(0, 0, 400, 300);
        
        // Draw some UI elements
        ctx.fillStyle = '#007bff';
        ctx.fillRect(50, 50, 300, 60);
        
        ctx.fillStyle = 'white';
        ctx.font = '20px Arial';
        ctx.fillText('Test UI Component', 120, 85);
        
        ctx.fillStyle = '#28a745';
        ctx.fillRect(50, 150, 100, 40);
        
        ctx.fillStyle = 'white';
        ctx.font = '16px Arial';
        ctx.fillText('Button', 75, 175);
        
        ctx.fillStyle = '#dc3545';
        ctx.fillRect(200, 150, 100, 40);
        
        ctx.fillStyle = 'white';
        ctx.fillText('Cancel', 225, 175);
        
        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'test-ui-screenshot.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
