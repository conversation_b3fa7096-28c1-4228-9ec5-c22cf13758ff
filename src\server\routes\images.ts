import express from 'express';
import multer from 'multer';
import rateLimit from 'express-rate-limit';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { S3Service } from '../services/s3';
import { DatabaseService } from '../services/database';
import { QueueService } from '../services/queue';

const router = express.Router();

// Rate limiting for upload endpoints
const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 uploads per windowMs
  message: {
    error: 'Too many upload attempts, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB default
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/jpg,image/png').split(',');
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type. Only ${allowedTypes.join(', ')} files are allowed.`));
    }
  },
});

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @route POST /api/v1/images/upload
 * @desc Upload a new image
 * @access Private
 */
router.post('/upload', uploadLimiter, upload.single('file'), async (req: AuthenticatedRequest, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'NO_FILE',
          message: 'No file provided',
        },
      });
    }

    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      });
    }

    const file = req.file;
    const { originalname, mimetype, size, buffer } = file;
    const { projectId, mode = 'auto' } = req.body;

    // Validate file size and dimensions
    const maxWidth = parseInt(process.env.MAX_IMAGE_WIDTH || '4096');
    const maxHeight = parseInt(process.env.MAX_IMAGE_HEIGHT || '4096');

    // Upload to storage
    const uploadResult = await S3Service.uploadImage(buffer, originalname, mimetype);

    // Save image to database
    const savedImage = await DatabaseService.createImage({
      userId: req.user.id,
      projectId: projectId || null,
      filename: uploadResult.key.split('/').pop() || originalname,
      originalFilename: originalname,
      mimeType: mimetype,
      size,
      width: 1920, // TODO: Extract from image metadata using Sharp
      height: 1080,
      s3Key: uploadResult.key,
      s3Bucket: uploadResult.bucket,
      mode: mode,
      previewUrl: uploadResult.url,
      thumbnailUrl: uploadResult.thumbnailUrl,
    });

    // Create image processing job
    try {
      await QueueService.addImageProcessingJob({
        imageId: savedImage.id,
        userId: req.user.id,
        s3Key: uploadResult.key,
        s3Bucket: uploadResult.bucket,
        mode: mode,
        settings: {}, // Add any processing settings here
      });
      console.log(`Image processing job created for image ${savedImage.id}`);
    } catch (jobError) {
      console.error('Failed to create processing job:', jobError);
      // Don't fail the upload if job creation fails
    }

    res.status(201).json({
      success: true,
      message: 'Image uploaded successfully',
      data: {
        imageId: savedImage.id,
        filename: savedImage.filename,
        originalFilename: savedImage.originalFilename,
        size: savedImage.size,
        dimensions: { width: savedImage.width, height: savedImage.height },
        mode: savedImage.mode,
        uploadedAt: savedImage.createdAt.toISOString(),
        processingStatus: savedImage.processingStatus,
        previewUrl: savedImage.previewUrl,
        thumbnailUrl: savedImage.thumbnailUrl,
        estimatedTokens: 3, // TODO: Calculate based on image complexity
        s3Key: savedImage.s3Key,
        s3Bucket: savedImage.s3Bucket,
      },
    });

  } catch (error) {
    console.error('Upload error:', error);
    
    if (error instanceof Error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'UPLOAD_FAILED',
          message: error.message,
        },
      });
    }
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to process upload',
      },
    });
  }
});

/**
 * @route GET /api/v1/images
 * @desc Get user's images
 * @access Private
 */
router.get('/', async (req: AuthenticatedRequest, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const { page = 1, limit = 20, projectId, status } = req.query;
    
    const result = await DatabaseService.getUserImages(req.user.id, {
      page: parseInt(page as string),
      limit: parseInt(limit as string),
      projectId: projectId as string,
      status: status as string,
    });

    // For simple requests (like dashboard), return just the images array
    // For paginated requests, return the full object with pagination
    const includePagination = req.query.includePagination === 'true';

    res.json({
      success: true,
      data: includePagination ? result : result.images
    });

  } catch (error) {
    console.error('Get images error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch images'
    });
  }
});

/**
 * @route GET /api/v1/images/:id
 * @desc Get specific image details
 * @access Private
 */
router.get('/:id', async (req: AuthenticatedRequest, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const image = await DatabaseService.getImageById(req.params.id, req.user.id);
    
    if (!image) {
      return res.status(404).json({
        success: false,
        error: 'Image not found'
      });
    }

    res.json({
      success: true,
      data: image
    });

  } catch (error) {
    console.error('Get image error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch image'
    });
  }
});

/**
 * @route DELETE /api/v1/images/:id
 * @desc Delete an image
 * @access Private
 */
router.delete('/:id', async (req: AuthenticatedRequest, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const deleted = await DatabaseService.deleteImage(req.params.id, req.user.id);
    
    if (!deleted) {
      return res.status(404).json({
        success: false,
        error: 'Image not found'
      });
    }

    res.json({
      success: true,
      message: 'Image deleted successfully'
    });

  } catch (error) {
    console.error('Delete image error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete image'
    });
  }
});

export default router;
